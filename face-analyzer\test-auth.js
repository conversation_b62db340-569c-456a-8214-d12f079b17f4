require('dotenv').config();
const https = require('https');
const crypto = require('crypto');

// Function to generate id_token using RSA encryption
function generateIdToken(clientId, clientSecret) {
  const timestamp = new Date().getTime();
  const dataToEncrypt = `client_id=${clientId}&timestamp=${timestamp}`;
  
  console.log('Data to encrypt:', dataToEncrypt);
  console.log('Timestamp:', timestamp);
  
  // Format the public key properly
  const pemKey = `-----BEGIN PUBLIC KEY-----\n${clientSecret.match(/.{1,64}/g).join('\n')}\n-----END PUBLIC KEY-----`;
  
  console.log('Formatted PEM key:');
  console.log(pemKey);

  // Test if the key is valid
  try {
    const testKey = crypto.createPublicKey(pemKey);
    console.log('Public key is valid');
    console.log('Key type:', testKey.asymmetricKeyType);
    console.log('Key size:', testKey.asymmetricKeySize);
  } catch (keyError) {
    console.error('Invalid public key:', keyError.message);
  }
  
  try {
    // Try with different padding options
    const encryptOptions = {
      key: pemKey,
      padding: crypto.constants.RSA_PKCS1_PADDING
    };

    const encrypted = crypto.publicEncrypt(encryptOptions, Buffer.from(dataToEncrypt));
    const base64Result = encrypted.toString('base64');
    console.log('Encryption successful, result length:', base64Result.length);
    console.log('ID Token:', base64Result);
    return base64Result;
  } catch (error) {
    console.error('Error generating id_token:', error);
    console.error('Error details:', error.message);
    throw new Error('Failed to generate id_token');
  }
}

// Test authentication
async function testAuth() {
  const clientId = process.env.PERFECT_CORP_API_KEY;
  const clientSecret = process.env.YOUCAM_API_SECRET;
  
  console.log('Client ID:', clientId);
  console.log('Client Secret length:', clientSecret?.length);
  console.log('Client Secret (first 50 chars):', clientSecret?.substring(0, 50));
  
  if (!clientId || !clientSecret) {
    console.error('Missing API credentials');
    return;
  }

  try {
    const idToken = generateIdToken(clientId, clientSecret);
    
    const authPayload = {
      client_id: clientId,
      id_token: idToken
    };

    console.log('\nAuth payload:');
    console.log(JSON.stringify(authPayload, null, 2));

    const options = {
      method: 'POST',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: '/s2s/v1.0/client/auth',
      headers: {
        'content-type': 'application/json'
      }
    };

    const req = https.request(options, function (res) {
      const chunks = [];
      
      res.on('data', function (chunk) {
        chunks.push(chunk);
      });
      
      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();
        
        console.log('\nAuth response status:', res.statusCode);
        console.log('Auth response headers:', res.headers);
        console.log('Auth response body:', responseText);
      });
    });
    
    req.on('error', function (error) {
      console.error('Auth request error:', error);
    });
    
    req.write(JSON.stringify(authPayload));
    req.end();

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAuth();
