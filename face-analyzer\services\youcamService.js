const https = require('https');
const crypto = require('crypto');

let cachedAccessToken = null;
let tokenExpiry = null;

// Function to generate id_token using RSA encryption
function generateIdToken(clientId, clientSecret) {
  const timestamp = new Date().getTime();
  const dataToEncrypt = `client_id=${clientId}&timestamp=${timestamp}`;

  // Format the public key properly
  const pemKey = `-----BEGIN PUBLIC KEY-----\n${clientSecret.match(/.{1,64}/g).join('\n')}\n-----END PUBLIC KEY-----`;

  try {
    // Use explicit RSA padding for compatibility
    const encryptOptions = {
      key: pemKey,
      padding: crypto.constants.RSA_PKCS1_PADDING
    };

    const encrypted = crypto.publicEncrypt(encryptOptions, Buffer.from(dataToEncrypt));
    const base64Result = encrypted.toString('base64');
    return base64Result;
  } catch (error) {
    console.error('Error generating id_token:', error);
    throw new Error('Failed to generate id_token');
  }
}

// Function to authenticate and get access token
async function getAccessToken() {
  // Check if we have a valid cached token (valid for 2 hours, refresh 10 minutes early)
  if (cachedAccessToken && tokenExpiry && Date.now() < tokenExpiry - 10 * 60 * 1000) {
    return cachedAccessToken;
  }

  const clientId = process.env.PERFECT_CORP_API_KEY;
  const clientSecret = process.env.YOUCAM_API_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('Missing API credentials');
  }

  console.log('Authenticating with YouCam API...');

  const idToken = generateIdToken(clientId, clientSecret);

  const authPayload = {
    client_id: clientId,
    id_token: idToken
  };

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.0/client/auth',
    headers: {
      'content-type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];

      res.on('data', function (chunk) {
        chunks.push(chunk);
      });

      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();

        console.log('Auth response status:', res.statusCode);

        try {
          const responseData = JSON.parse(responseText);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            cachedAccessToken = responseData.result.access_token;
            tokenExpiry = Date.now() + 2 * 60 * 60 * 1000; // 2 hours from now
            resolve(cachedAccessToken);
          } else {
            reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseData.error || responseText}`));
          }
        } catch (parseError) {
          reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseText}`));
        }
      });
    });

    req.on('error', function (error) {
      console.error('Auth request error:', error);
      reject(new Error(`Authentication request failed: ${error.message}`));
    });

    req.write(JSON.stringify(authPayload));
    req.end();
  });
}

exports.uploadImage = async (imageBase64) => {
  try {
    console.log('Uploading image to YouCam...');
    console.log('API Key present:', !!process.env.PERFECT_CORP_API_KEY);
    console.log('API Secret present:', !!process.env.YOUCAM_API_SECRET);

    // Get access token first
    const accessToken = await getAccessToken();
    console.log('Access token obtained successfully');

    // Convert base64 to buffer to calculate file size
    const imageBuffer = Buffer.from(imageBase64, 'base64');
    const fileSize = imageBuffer.length;

    // Detect image type and file extension
    let contentType = 'image/jpg';
    let fileName = 'image.jpg';

    // Check if we can detect the image type from the base64 header
    const base64Header = imageBase64.substring(0, 50);
    if (base64Header.includes('PNG') || base64Header.startsWith('iVBORw')) {
      contentType = 'image/png';
      fileName = 'image.png';
    } else if (base64Header.includes('GIF') || base64Header.startsWith('R0lGOD')) {
      contentType = 'image/gif';
      fileName = 'image.gif';
    }

    console.log('Detected image type:', contentType);
    console.log('File size:', fileSize, 'bytes');

    // Prepare the payload according to API documentation
    const payload = {
      files: [{
        content_type: contentType,
        file_name: fileName,
        file_size: fileSize
      }]
    };

    console.log('Sending payload:', JSON.stringify(payload, null, 2));

    // Use native https module as shown in documentation
    const options = {
      method: 'POST',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: '/s2s/v1.1/file/face-attr-analysis',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'content-type': 'application/json'
      }
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, function (res) {
        const chunks = [];

        res.on('data', function (chunk) {
          chunks.push(chunk);
        });

        res.on('end', function () {
          const body = Buffer.concat(chunks);
          const responseText = body.toString();

          console.log('Upload response status:', res.statusCode);
          console.log('Upload response headers:', res.headers);
          console.log('Upload response body:', responseText);

          try {
            const responseData = JSON.parse(responseText);

            if (res.statusCode >= 200 && res.statusCode < 300) {
              // Assuming the API returns a file_id or similar identifier
              resolve(responseData.file_id || responseData);
            } else {
              console.error('API Error Response:', responseData);
              reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseData.error || responseText}`));
            }
          } catch (parseError) {
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(responseText);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
            }
          }
        });
      });

      req.on('error', function (error) {
        console.error('Request error:', error);
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.write(JSON.stringify(payload));
      req.end();
    });

  } catch (error) {
    console.error('Upload error details:', error);
    throw new Error(`Image upload failed: ${error.message}`);
  }
};

exports.startAnalysis = async (fileId) => {
  try {
    console.log('Starting analysis for file ID:', fileId);

    // Get access token
    const accessToken = await getAccessToken();

    const payload = { file_id: fileId };

    const options = {
      method: 'POST',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: '/s2s/v1.0/task/face-attr-analysis',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'content-type': 'application/json'
      }
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, function (res) {
        const chunks = [];

        res.on('data', function (chunk) {
          chunks.push(chunk);
        });

        res.on('end', function () {
          const body = Buffer.concat(chunks);
          const responseText = body.toString();

          console.log('Analysis start response:', responseText);

          try {
            const responseData = JSON.parse(responseText);

            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(responseData.task_id);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseText}`));
            }
          } catch (parseError) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
          }
        });
      });

      req.on('error', function (error) {
        console.error('Request error:', error);
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.write(JSON.stringify(payload));
      req.end();
    });

  } catch (error) {
    console.error('Analysis start error:', error);
    throw new Error(`Analysis start failed: ${error.message}`);
  }
};

exports.getAnalysisResult = async (taskId) => {
  try {
    console.log('Getting analysis result for task ID:', taskId);
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds timeout

    // Get access token once for all polling attempts
    const accessToken = await getAccessToken();

    while (attempts < maxAttempts) {
      const options = {
        method: 'GET',
        hostname: 'yce-api-01.perfectcorp.com',
        port: null,
        path: `/s2s/v1.0/task/face-attr-analysis?task_id=${encodeURIComponent(taskId)}`,
        headers: { Authorization: `Bearer ${accessToken}` }
      };

      const result = await new Promise((resolve, reject) => {
        const req = https.request(options, function (res) {
          const chunks = [];

          res.on('data', function (chunk) {
            chunks.push(chunk);
          });

          res.on('end', function () {
            const body = Buffer.concat(chunks);
            const responseText = body.toString();

            try {
              const responseData = JSON.parse(responseText);

              if (res.statusCode >= 200 && res.statusCode < 300) {
                resolve(responseData);
              } else {
                reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseText}`));
              }
            } catch (parseError) {
              reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
            }
          });
        });

        req.on('error', function (error) {
          console.error('Request error:', error);
          reject(new Error(`Request failed: ${error.message}`));
        });

        req.end();
      });

      const { status, result: analysisResult } = result;
      console.log(`Analysis status (attempt ${attempts + 1}):`, status);

      if (status === 'completed') {
        console.log('Analysis completed successfully');
        return analysisResult;
      }
      if (status === 'failed') {
        throw new Error('Analysis failed');
      }

      attempts++;
      await new Promise(r => setTimeout(r, 1000));
    }

    throw new Error('Analysis timeout - took too long to complete');
  } catch (error) {
    console.error('Analysis result error:', error);
    throw new Error(`Getting analysis result failed: ${error.message}`);
  }
};
